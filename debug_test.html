<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        #log { background: #f5f5f5; padding: 10px; height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>聊天平台调试测试</h1>
    
    <div class="test-section">
        <h2>1. Socket.IO 加载测试</h2>
        <div id="socketio-status">检查中...</div>
    </div>
    
    <div class="test-section">
        <h2>2. localStorage 测试</h2>
        <div id="storage-status">检查中...</div>
        <button onclick="testStorage()">测试存储</button>
        <button onclick="clearStorage()">清空存储</button>
    </div>
    
    <div class="test-section">
        <h2>3. 服务器连接测试</h2>
        <div id="server-status">检查中...</div>
        <button onclick="testServer()">测试连接</button>
    </div>
    
    <div class="test-section">
        <h2>4. 日志</h2>
        <div id="log"></div>
    </div>

    <script src="http://localhost:5000/static/js/socket.io.min.js"></script>
    <script>
        const log = (message, type = 'info') => {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            logDiv.innerHTML += `<div class="${className}">[${time}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        };

        // 测试Socket.IO
        function testSocketIO() {
            const statusDiv = document.getElementById('socketio-status');
            if (typeof io === 'undefined') {
                statusDiv.innerHTML = '<span class="error">❌ Socket.IO 未加载</span>';
                log('Socket.IO 客户端库未加载', 'error');
                return false;
            } else {
                statusDiv.innerHTML = '<span class="success">✅ Socket.IO 已加载</span>';
                log('Socket.IO 客户端库已成功加载', 'success');
                return true;
            }
        }

        // 测试localStorage
        function testStorage() {
            const statusDiv = document.getElementById('storage-status');
            try {
                // 测试存储和读取
                localStorage.setItem('test_key', JSON.stringify({test: 'value'}));
                const retrieved = localStorage.getItem('test_key');
                const parsed = JSON.parse(retrieved);
                
                if (parsed.test === 'value') {
                    statusDiv.innerHTML = '<span class="success">✅ localStorage 正常工作</span>';
                    log('localStorage 测试通过', 'success');
                } else {
                    throw new Error('数据不匹配');
                }
                
                // 清理测试数据
                localStorage.removeItem('test_key');
            } catch (error) {
                statusDiv.innerHTML = '<span class="error">❌ localStorage 错误</span>';
                log(`localStorage 测试失败: ${error.message}`, 'error');
            }
        }

        // 清空存储
        function clearStorage() {
            try {
                localStorage.clear();
                log('localStorage 已清空', 'success');
            } catch (error) {
                log(`清空 localStorage 失败: ${error.message}`, 'error');
            }
        }

        // 测试服务器连接
        function testServer() {
            const statusDiv = document.getElementById('server-status');
            
            if (typeof io === 'undefined') {
                statusDiv.innerHTML = '<span class="error">❌ Socket.IO 未加载，无法测试连接</span>';
                return;
            }

            statusDiv.innerHTML = '<span class="warning">🔄 正在连接...</span>';
            log('尝试连接到服务器...', 'info');

            const socket = io('http://localhost:5000');

            socket.on('connect', () => {
                statusDiv.innerHTML = '<span class="success">✅ 服务器连接成功</span>';
                log(`成功连接到服务器，Session ID: ${socket.id}`, 'success');
                socket.disconnect();
            });

            socket.on('connect_error', (error) => {
                statusDiv.innerHTML = '<span class="error">❌ 服务器连接失败</span>';
                log(`服务器连接失败: ${error.message}`, 'error');
            });

            socket.on('disconnect', () => {
                log('与服务器断开连接', 'info');
            });
        }

        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', () => {
            log('开始调试测试...', 'info');
            
            // 延迟一点确保所有脚本都加载完成
            setTimeout(() => {
                testSocketIO();
                testStorage();
            }, 100);
        });
    </script>
</body>
</html>
