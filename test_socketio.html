<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Socket.IO 测试</title>
</head>
<body>
    <h1>Socket.IO 连接测试</h1>
    <div id="status">正在连接...</div>
    <div id="log"></div>

    <script src="http://localhost:5000/socket.io/socket.io.js"></script>
    <script>
        const statusDiv = document.getElementById('status');
        const logDiv = document.getElementById('log');
        
        function log(message) {
            console.log(message);
            logDiv.innerHTML += '<p>' + new Date().toLocaleTimeString() + ': ' + message + '</p>';
        }
        
        // 检查Socket.IO是否加载
        if (typeof io === 'undefined') {
            statusDiv.textContent = 'Socket.IO 未加载';
            statusDiv.style.color = 'red';
            log('错误: Socket.IO 客户端库未加载');
        } else {
            statusDiv.textContent = 'Socket.IO 已加载，正在连接...';
            statusDiv.style.color = 'orange';
            log('Socket.IO 客户端库已加载');
            
            // 创建连接
            const socket = io('http://localhost:5000');
            
            socket.on('connect', () => {
                statusDiv.textContent = '已连接到服务器';
                statusDiv.style.color = 'green';
                log('成功连接到服务器，Session ID: ' + socket.id);
            });
            
            socket.on('disconnect', () => {
                statusDiv.textContent = '与服务器断开连接';
                statusDiv.style.color = 'red';
                log('与服务器断开连接');
            });
            
            socket.on('connect_error', (error) => {
                statusDiv.textContent = '连接错误';
                statusDiv.style.color = 'red';
                log('连接错误: ' + error.message);
            });
        }
    </script>
</body>
</html>
