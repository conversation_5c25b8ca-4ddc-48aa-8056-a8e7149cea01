// 工具函数库

// 全局状态管理
window.AppState = {
    user: null,
    currentRoom: null,
    socket: null,
    isAuthenticated: false,
    isAdmin: false,
    rooms: [],
    messages: new Map(),
    typingUsers: new Set(),
    onlineUsers: new Set()
};

// 本地存储管理
const Storage = {
    set(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
        } catch (error) {
            console.error('存储数据失败:', error);
        }
    },
    
    get(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(key);
            if (!item || item === 'undefined' || item === 'null') {
                return defaultValue;
            }
            return JSON.parse(item);
        } catch (error) {
            console.error(`读取数据失败 (key: ${key}, value: ${localStorage.getItem(key)}):`, error);
            // 清除无效数据
            localStorage.removeItem(key);
            return defaultValue;
        }
    },
    
    remove(key) {
        try {
            localStorage.removeItem(key);
        } catch (error) {
            console.error('删除数据失败:', error);
        }
    },
    
    clear() {
        try {
            localStorage.clear();
        } catch (error) {
            console.error('清空数据失败:', error);
        }
    },

    // 清理无效数据
    cleanup() {
        const keys = ['auth_token', 'session_id', 'user_data'];
        keys.forEach(key => {
            const item = localStorage.getItem(key);
            if (item === 'undefined' || item === 'null') {
                console.log(`清理无效数据: ${key} = ${item}`);
                localStorage.removeItem(key);
            }
        });
    }
};

// DOM 操作工具
const DOM = {
    $(selector) {
        return document.querySelector(selector);
    },
    
    $$(selector) {
        return document.querySelectorAll(selector);
    },
    
    create(tag, attributes = {}, content = '') {
        const element = document.createElement(tag);
        
        Object.entries(attributes).forEach(([key, value]) => {
            if (key === 'className') {
                element.className = value;
            } else if (key === 'innerHTML') {
                element.innerHTML = value;
            } else if (key.startsWith('on')) {
                element.addEventListener(key.slice(2).toLowerCase(), value);
            } else {
                element.setAttribute(key, value);
            }
        });
        
        if (content) {
            element.textContent = content;
        }
        
        return element;
    },
    
    show(element) {
        if (typeof element === 'string') {
            element = this.$(element);
        }
        if (element) {
            element.style.display = '';
        }
    },
    
    hide(element) {
        if (typeof element === 'string') {
            element = this.$(element);
        }
        if (element) {
            element.style.display = 'none';
        }
    },
    
    toggle(element) {
        if (typeof element === 'string') {
            element = this.$(element);
        }
        if (element) {
            element.style.display = element.style.display === 'none' ? '' : 'none';
        }
    },
    
    addClass(element, className) {
        if (typeof element === 'string') {
            element = this.$(element);
        }
        if (element) {
            element.classList.add(className);
        }
    },
    
    removeClass(element, className) {
        if (typeof element === 'string') {
            element = this.$(element);
        }
        if (element) {
            element.classList.remove(className);
        }
    },
    
    toggleClass(element, className) {
        if (typeof element === 'string') {
            element = this.$(element);
        }
        if (element) {
            element.classList.toggle(className);
        }
    },
    
    hasClass(element, className) {
        if (typeof element === 'string') {
            element = this.$(element);
        }
        return element ? element.classList.contains(className) : false;
    }
};

// 时间格式化工具
const TimeUtils = {
    formatTime(date) {
        if (typeof date === 'string') {
            date = new Date(date);
        }
        
        const now = new Date();
        const diff = now - date;
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);
        
        if (minutes < 1) {
            return '刚刚';
        } else if (minutes < 60) {
            return `${minutes}分钟前`;
        } else if (hours < 24) {
            return `${hours}小时前`;
        } else if (days < 7) {
            return `${days}天前`;
        } else {
            return date.toLocaleDateString('zh-CN');
        }
    },
    
    formatDateTime(date) {
        if (typeof date === 'string') {
            date = new Date(date);
        }
        
        const now = new Date();
        const isToday = date.toDateString() === now.toDateString();
        const isYesterday = new Date(now - 86400000).toDateString() === date.toDateString();
        
        const timeStr = date.toLocaleTimeString('zh-CN', { 
            hour: '2-digit', 
            minute: '2-digit' 
        });
        
        if (isToday) {
            return timeStr;
        } else if (isYesterday) {
            return `昨天 ${timeStr}`;
        } else {
            return `${date.toLocaleDateString('zh-CN')} ${timeStr}`;
        }
    }
};

// 文件工具
const FileUtils = {
    formatSize(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    getFileIcon(filename) {
        const ext = filename.split('.').pop().toLowerCase();
        const iconMap = {
            // 图片
            'jpg': '🖼️', 'jpeg': '🖼️', 'png': '🖼️', 'gif': '🖼️', 'webp': '🖼️',
            // 音频
            'mp3': '🎵', 'wav': '🎵', 'ogg': '🎵', 'm4a': '🎵',
            // 视频
            'mp4': '🎬', 'avi': '🎬', 'mov': '🎬', 'wmv': '🎬',
            // 文档
            'pdf': '📄', 'doc': '📝', 'docx': '📝', 'txt': '📝',
            'xls': '📊', 'xlsx': '📊', 'ppt': '📊', 'pptx': '📊',
            // 压缩包
            'zip': '📦', 'rar': '📦', '7z': '📦', 'tar': '📦'
        };
        
        return iconMap[ext] || '📎';
    },
    
    isImage(filename) {
        const ext = filename.split('.').pop().toLowerCase();
        return ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'].includes(ext);
    },
    
    isAudio(filename) {
        const ext = filename.split('.').pop().toLowerCase();
        return ['mp3', 'wav', 'ogg', 'm4a', 'aac'].includes(ext);
    }
};

// 字符串工具
const StringUtils = {
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },
    
    truncate(text, length = 50) {
        if (text.length <= length) return text;
        return text.substring(0, length) + '...';
    },
    
    generateId() {
        return Math.random().toString(36).substring(2) + Date.now().toString(36);
    },
    
    formatMention(text) {
        return text.replace(/@(\w+)/g, '<span class="mention">@$1</span>');
    },
    
    formatLinks(text) {
        const urlRegex = /(https?:\/\/[^\s]+)/g;
        return text.replace(urlRegex, '<a href="$1" target="_blank" rel="noopener">$1</a>');
    }
};

// 事件管理器
class EventEmitter {
    constructor() {
        this.events = {};
    }
    
    on(event, callback) {
        if (!this.events[event]) {
            this.events[event] = [];
        }
        this.events[event].push(callback);
    }
    
    off(event, callback) {
        if (!this.events[event]) return;
        
        const index = this.events[event].indexOf(callback);
        if (index > -1) {
            this.events[event].splice(index, 1);
        }
    }
    
    emit(event, ...args) {
        if (!this.events[event]) return;
        
        this.events[event].forEach(callback => {
            try {
                callback(...args);
            } catch (error) {
                console.error(`事件处理错误 (${event}):`, error);
            }
        });
    }
}

// 全局事件总线
window.EventBus = new EventEmitter();

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 深拷贝
function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj);
    if (obj instanceof Array) return obj.map(item => deepClone(item));
    if (typeof obj === 'object') {
        const clonedObj = {};
        Object.keys(obj).forEach(key => {
            clonedObj[key] = deepClone(obj[key]);
        });
        return clonedObj;
    }
}

// 错误处理
function handleError(error, context = '') {
    console.error(`错误 ${context}:`, error);
    
    // 显示用户友好的错误消息
    let message = '操作失败，请稍后重试';
    
    if (error.message) {
        if (error.message.includes('网络')) {
            message = '网络连接失败，请检查网络设置';
        } else if (error.message.includes('权限')) {
            message = '权限不足，请重新登录';
        } else if (error.message.includes('服务器')) {
            message = '服务器错误，请稍后重试';
        }
    }
    
    showNotification(message, 'error');
}

// 通知系统
class NotificationManager {
    constructor() {
        this.container = null;
        this.notifications = new Map();
        this.init();
    }

    init() {
        // 创建通知容器
        this.container = DOM.$('#notifications-container');
        if (!this.container) {
            this.container = DOM.create('div', { id: 'notifications-container' });
            document.body.appendChild(this.container);
        }
    }

    show(message, type = 'info', duration = 5000) {
        const id = StringUtils.generateId();

        const notification = DOM.create('div', {
            className: `notification ${type}`,
            innerHTML: `
                <div class="notification-header">
                    <div class="notification-title">${this.getTypeTitle(type)}</div>
                    <button class="notification-close" onclick="window.notificationManager.hide('${id}')">&times;</button>
                </div>
                <div class="notification-message">${StringUtils.escapeHtml(message)}</div>
            `
        });

        this.container.appendChild(notification);
        this.notifications.set(id, notification);

        // 自动隐藏
        if (duration > 0) {
            setTimeout(() => {
                this.hide(id);
            }, duration);
        }

        return id;
    }

    hide(id) {
        const notification = this.notifications.get(id);
        if (notification) {
            notification.style.animation = 'notificationSlideOut 0.3s ease-out forwards';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
                this.notifications.delete(id);
            }, 300);
        }
    }

    clear() {
        this.notifications.forEach((notification, id) => {
            this.hide(id);
        });
    }

    getTypeTitle(type) {
        const titles = {
            success: '成功',
            error: '错误',
            warning: '警告',
            info: '提示'
        };
        return titles[type] || '通知';
    }
}

// 创建全局通知管理器
window.notificationManager = new NotificationManager();

// 全局通知函数
window.showNotification = function(message, type = 'info', duration = 5000) {
    return window.notificationManager.show(message, type, duration);
};

// 导出到全局
window.Storage = Storage;
window.DOM = DOM;
window.TimeUtils = TimeUtils;
window.FileUtils = FileUtils;
window.StringUtils = StringUtils;
window.debounce = debounce;
window.throttle = throttle;
window.deepClone = deepClone;
window.handleError = handleError;
