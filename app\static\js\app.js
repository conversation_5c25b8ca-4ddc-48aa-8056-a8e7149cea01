// 主应用程序

class ChatApp {
    constructor() {
        this.isInitialized = false;
        this.init();
    }
    
    async init() {
        try {
            // 显示加载屏幕
            this.showLoadingScreen();
            
            // 初始化应用
            await this.initializeApp();
            
            // 隐藏加载屏幕
            this.hideLoadingScreen();
            
            // 根据认证状态显示相应界面
            if (window.authManager && window.authManager.isAuthenticated()) {
                window.authManager.showChatScreen();
                await this.initializeChat();
            } else {
                window.authManager.showLoginScreen();
            }
            
            this.isInitialized = true;
            
        } catch (error) {
            console.error('应用初始化失败:', error);
            this.hideLoadingScreen();
            showNotification('应用初始化失败，请刷新页面重试', 'error');
        }
    }
    
    // 显示加载屏幕
    showLoadingScreen() {
        DOM.show(DOM.$('#loading-screen'));
        DOM.hide(DOM.$('#app'));
    }
    
    // 隐藏加载屏幕
    hideLoadingScreen() {
        DOM.hide(DOM.$('#loading-screen'));
        DOM.show(DOM.$('#app'));
    }
    
    // 初始化应用
    async initializeApp() {
        // 清理无效的localStorage数据
        if (typeof Storage !== 'undefined' && Storage.cleanup) {
            Storage.cleanup();
        }

        // 设置全局事件监听器
        this.setupGlobalEventListeners();

        // 初始化侧边栏导航
        this.initializeSidebarNavigation();

        // 初始化输入区域
        this.initializeInputArea();

        // 监听聊天初始化事件
        EventBus.on('chat:initialized', () => {
            this.initializeChat();
        });
    }
    
    // 设置全局事件监听器
    setupGlobalEventListeners() {
        // 窗口大小变化
        window.addEventListener('resize', debounce(() => {
            this.handleResize();
        }, 250));
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
        
        // 点击外部关闭模态框
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                this.closeModal();
            }
        });
        
        // 阻止默认的拖拽行为
        document.addEventListener('dragover', (e) => {
            e.preventDefault();
        });
        
        document.addEventListener('drop', (e) => {
            e.preventDefault();
            this.handleFileDrop(e);
        });
    }
    
    // 初始化侧边栏导航
    initializeSidebarNavigation() {
        const navButtons = DOM.$$('.nav-btn');
        navButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const view = e.target.dataset.view;
                this.switchSidebarView(view);
            });
        });
    }
    
    // 切换侧边栏视图
    switchSidebarView(view) {
        // 更新导航按钮状态
        DOM.$$('.nav-btn').forEach(btn => {
            DOM.removeClass(btn, 'active');
        });
        DOM.addClass(DOM.$(`[data-view="${view}"]`), 'active');
        
        // 更新内容显示
        DOM.$$('.sidebar-content').forEach(content => {
            DOM.removeClass(content, 'active');
        });
        DOM.addClass(DOM.$(`#${view}-view`), 'active');
        
        // 触发视图切换事件
        EventBus.emit('sidebar:viewChanged', view);
    }
    
    // 初始化输入区域
    initializeInputArea() {
        const messageInput = DOM.$('#message-input');
        if (messageInput) {
            // 自动调整高度
            messageInput.addEventListener('input', () => {
                this.adjustTextareaHeight(messageInput);
            });
            
            // 处理快捷键
            messageInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });
        }
    }
    
    // 调整文本框高度
    adjustTextareaHeight(textarea) {
        textarea.style.height = 'auto';
        const maxHeight = 120; // 最大高度
        const newHeight = Math.min(textarea.scrollHeight, maxHeight);
        textarea.style.height = newHeight + 'px';
    }
    
    // 初始化聊天功能
    async initializeChat() {
        try {
            // 初始化Socket连接
            await this.initializeSocket();
            
            // 加载房间列表
            if (window.roomsManager) {
                await window.roomsManager.loadRooms();
            }
            
            // 加载个人信息
            if (window.profileManager) {
                await window.profileManager.loadProfile();
            }
            
        } catch (error) {
            console.error('聊天初始化失败:', error);
            showNotification('聊天功能初始化失败', 'error');
        }
    }
    
    // 初始化Socket连接
    async initializeSocket() {
        // 等待Socket.IO加载
        let retries = 0;
        const maxRetries = 10;

        while (typeof io === 'undefined' && retries < maxRetries) {
            console.log(`等待Socket.IO加载... (${retries + 1}/${maxRetries})`);
            await new Promise(resolve => setTimeout(resolve, 100));
            retries++;
        }

        if (typeof io === 'undefined') {
            console.error('Socket.IO客户端库加载失败');
            throw new Error('Socket.IO客户端库加载失败');
        }

        if (AppState.socket) {
            AppState.socket.disconnect();
        }

        // 创建Socket连接
        AppState.socket = io({
            auth: {
                token: Storage.get('auth_token'),
                session_id: Storage.get('session_id')
            }
        });

        // 设置Socket事件监听器
        this.setupSocketEventListeners();
    }
    
    // 设置Socket事件监听器
    setupSocketEventListeners() {
        const socket = AppState.socket;
        
        socket.on('connect', () => {
            console.log('Socket连接成功');
            showNotification('连接成功', 'success', 2000);
        });
        
        socket.on('disconnect', () => {
            console.log('Socket连接断开');
            showNotification('连接断开，正在重连...', 'warning');
        });
        
        socket.on('connect_error', (error) => {
            console.error('Socket连接错误:', error);
            showNotification('连接失败，请检查网络', 'error');
        });
        
        // 消息相关事件
        socket.on('new_message', (data) => {
            EventBus.emit('message:received', data);
        });
        
        socket.on('message_recalled', (data) => {
            EventBus.emit('message:recalled', data);
        });
        
        socket.on('message_reaction', (data) => {
            EventBus.emit('message:reaction', data);
        });
        
        // 用户状态事件
        socket.on('user_joined', (data) => {
            EventBus.emit('user:joined', data);
        });
        
        socket.on('user_left', (data) => {
            EventBus.emit('user:left', data);
        });
        
        socket.on('user_typing', (data) => {
            EventBus.emit('user:typing', data);
        });
        
        socket.on('user_stop_typing', (data) => {
            EventBus.emit('user:stopTyping', data);
        });
        
        // 房间事件
        socket.on('room_updated', (data) => {
            EventBus.emit('room:updated', data);
        });
        
        // 问卷事件
        socket.on('questionnaire_sent', (data) => {
            EventBus.emit('questionnaire:sent', data);
        });
    }
    
    // 发送消息
    sendMessage() {
        if (window.chatManager) {
            window.chatManager.sendMessage();
        }
    }
    
    // 处理窗口大小变化
    handleResize() {
        // 移动端侧边栏处理
        if (window.innerWidth <= 768) {
            const sidebar = DOM.$('.sidebar');
            if (sidebar && !DOM.hasClass(sidebar, 'open')) {
                // 在移动端自动关闭侧边栏
            }
        }
    }
    
    // 处理键盘快捷键
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + K: 快速搜索
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            // 实现快速搜索功能
        }
        
        // Escape: 关闭模态框
        if (e.key === 'Escape') {
            this.closeModal();
        }
    }
    
    // 处理文件拖拽
    handleFileDrop(e) {
        const files = Array.from(e.dataTransfer.files);
        if (files.length > 0 && AppState.currentRoom) {
            // 处理文件上传
            if (window.fileManager) {
                window.fileManager.handleFileUpload(files);
            }
        }
    }
    
    // 显示模态框
    showModal(content, title = '') {
        const modalContainer = DOM.$('#modal-container');
        
        const modal = DOM.create('div', {
            className: 'modal-overlay',
            innerHTML: `
                <div class="modal">
                    <div class="modal-header">
                        <div class="modal-title">${StringUtils.escapeHtml(title)}</div>
                        <button class="modal-close" onclick="window.app.closeModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                </div>
            `
        });
        
        modalContainer.appendChild(modal);
        
        // 聚焦到第一个输入框
        setTimeout(() => {
            const firstInput = modal.querySelector('input, textarea, select');
            if (firstInput) {
                firstInput.focus();
            }
        }, 100);
    }
    
    // 关闭模态框
    closeModal() {
        const modalContainer = DOM.$('#modal-container');
        if (modalContainer) {
            modalContainer.innerHTML = '';
        }
    }
    
    // 切换侧边栏（移动端）
    toggleSidebar() {
        const sidebar = DOM.$('.sidebar');
        if (sidebar) {
            DOM.toggleClass(sidebar, 'open');
        }
    }
    
    // 显示设置
    showSettings() {
        const settingsContent = `
            <div class="form-group">
                <label class="form-label">主题设置</label>
                <select class="form-select" id="theme-select">
                    <option value="light">浅色主题</option>
                    <option value="dark">深色主题</option>
                    <option value="auto">跟随系统</option>
                </select>
            </div>
            <div class="form-group">
                <div class="form-checkbox">
                    <input type="checkbox" id="sound-enabled">
                    <label for="sound-enabled">启用消息提示音</label>
                </div>
            </div>
            <div class="form-group">
                <div class="form-checkbox">
                    <input type="checkbox" id="desktop-notifications">
                    <label for="desktop-notifications">启用桌面通知</label>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="window.app.closeModal()">取消</button>
                <button class="btn btn-primary" onclick="window.app.saveSettings()">保存</button>
            </div>
        `;
        
        this.showModal(settingsContent, '设置');
    }
    
    // 保存设置
    saveSettings() {
        const theme = DOM.$('#theme-select').value;
        const soundEnabled = DOM.$('#sound-enabled').checked;
        const desktopNotifications = DOM.$('#desktop-notifications').checked;
        
        Storage.set('settings', {
            theme,
            soundEnabled,
            desktopNotifications
        });
        
        showNotification('设置已保存', 'success');
        this.closeModal();
    }
}

// 全局函数
window.toggleSidebar = function() {
    window.app.toggleSidebar();
};

window.showSettings = function() {
    window.app.showSettings();
};

window.sendMessage = function() {
    window.app.sendMessage();
};

window.showFileUpload = function() {
    if (window.fileManager) {
        window.fileManager.showFileUploadDialog();
    } else {
        showNotification('文件管理器未初始化', 'error');
    }
};

window.showEmoji = function() {
    showNotification('表情功能开发中', 'info');
};

window.showCreateRoom = function() {
    if (window.roomsManager) {
        window.roomsManager.showCreateRoomDialog();
    } else {
        showNotification('房间管理器未初始化', 'error');
    }
};

window.editProfile = function() {
    if (window.profileManager) {
        window.profileManager.showEditDialog();
    } else {
        showNotification('个人信息管理器未初始化', 'error');
    }
};

window.createQuestionnaire = function() {
    if (window.questionnaireManager) {
        window.questionnaireManager.showCreateDialog();
    } else {
        showNotification('问卷管理器未初始化', 'error');
    }
};

window.showRoomSettings = function() {
    showNotification('房间设置功能开发中', 'info');
};

window.logout = function() {
    if (window.authManager) {
        window.authManager.logout();
    } else {
        showNotification('认证管理器未初始化', 'error');
    }
};

// 处理登录表单
window.handleLogin = function() {
    if (window.authManager) {
        window.authManager.handleLogin();
    }
};

// 处理注册表单
window.handleRegister = function() {
    if (window.authManager) {
        window.authManager.handleRegister();
    }
};

// 处理匿名登录
window.handleAnonymous = function() {
    if (window.authManager) {
        window.authManager.handleAnonymous();
    }
};

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    // 确保所有脚本都已加载
    setTimeout(() => {
        window.app = new ChatApp();
    }, 100);
});
