python_socketio-5.10.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
python_socketio-5.10.0.dist-info/LICENSE,sha256=yel9Pbwfu82094CLKCzWRtuIev9PUxP-a76NTDFAWpw,1082
python_socketio-5.10.0.dist-info/METADATA,sha256=cfaLtj6WTsImc8kkTxhHJspLt_R-aV5nf3iFC6Vdzmg,3213
python_socketio-5.10.0.dist-info/RECORD,,
python_socketio-5.10.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
python_socketio-5.10.0.dist-info/WHEEL,sha256=yQN5g4mg4AybRjkgi-9yy4iQEFibGQmlz78Pik5Or-A,92
python_socketio-5.10.0.dist-info/top_level.txt,sha256=xWd-HVUanhys_VzQQTRTRZBX8W448ayFytYf1Zffivs,9
socketio/__init__.py,sha256=DXxtwPIqHFIqV4BGTgJ86OvCXD6Mth3PxBYhFoJ1_7g,1269
socketio/__pycache__/__init__.cpython-312.pyc,,
socketio/__pycache__/admin.cpython-312.pyc,,
socketio/__pycache__/asgi.cpython-312.pyc,,
socketio/__pycache__/async_admin.cpython-312.pyc,,
socketio/__pycache__/async_aiopika_manager.cpython-312.pyc,,
socketio/__pycache__/async_client.cpython-312.pyc,,
socketio/__pycache__/async_manager.cpython-312.pyc,,
socketio/__pycache__/async_namespace.cpython-312.pyc,,
socketio/__pycache__/async_pubsub_manager.cpython-312.pyc,,
socketio/__pycache__/async_redis_manager.cpython-312.pyc,,
socketio/__pycache__/async_server.cpython-312.pyc,,
socketio/__pycache__/async_simple_client.cpython-312.pyc,,
socketio/__pycache__/base_client.cpython-312.pyc,,
socketio/__pycache__/base_manager.cpython-312.pyc,,
socketio/__pycache__/base_namespace.cpython-312.pyc,,
socketio/__pycache__/base_server.cpython-312.pyc,,
socketio/__pycache__/client.cpython-312.pyc,,
socketio/__pycache__/exceptions.cpython-312.pyc,,
socketio/__pycache__/kafka_manager.cpython-312.pyc,,
socketio/__pycache__/kombu_manager.cpython-312.pyc,,
socketio/__pycache__/manager.cpython-312.pyc,,
socketio/__pycache__/middleware.cpython-312.pyc,,
socketio/__pycache__/msgpack_packet.cpython-312.pyc,,
socketio/__pycache__/namespace.cpython-312.pyc,,
socketio/__pycache__/packet.cpython-312.pyc,,
socketio/__pycache__/pubsub_manager.cpython-312.pyc,,
socketio/__pycache__/redis_manager.cpython-312.pyc,,
socketio/__pycache__/server.cpython-312.pyc,,
socketio/__pycache__/simple_client.cpython-312.pyc,,
socketio/__pycache__/tornado.cpython-312.pyc,,
socketio/__pycache__/zmq_manager.cpython-312.pyc,,
socketio/admin.py,sha256=9B601337UMTh2NIFzVrlFyImmSEtegJoXYFDlEwGmYk,16473
socketio/asgi.py,sha256=Ti38pUt9TMUDZfc_l9DI3ZN9l50XoOK0gjSsz1m_-2I,1807
socketio/async_admin.py,sha256=Swn4s154pc7QTWCWNEbSM2psAmhEuvgobIn1Vf7Y0y8,16850
socketio/async_aiopika_manager.py,sha256=DaBUjGRYaNIsOsk2xNjWylUsz2egmTAFFUiQkV6mNmk,5193
socketio/async_client.py,sha256=RaGrdEXBgeGs5rpCihUpwPfZkAHQfdC50TmiZExC-os,25906
socketio/async_manager.py,sha256=JiKiI01wOKsX6_v4VJpgBvlxYFDnCA3vpA_lGDuA0YI,4468
socketio/async_namespace.py,sha256=fuIuIQDUL-lXyD9lm70W2QZr1wA_UMkOb-hQzE5P6u4,10616
socketio/async_pubsub_manager.py,sha256=v5HcoZD5Kn1AVFB-6AwTcIX05H8G_4s54rmm3JoJu0I,10865
socketio/async_redis_manager.py,sha256=UZXKunvbSk8neRVhGqigQF5S0WwLYTKV0BKondnV_yY,4299
socketio/async_server.py,sha256=4asvrToGRQ2YqjXUL6i88wOHNz_Ya-xMKhdkxO6GmzM,35155
socketio/async_simple_client.py,sha256=45wVUps7U-24rFnhd0lxNYClysilJvYzDh-b1BNyJTA,8907
socketio/base_client.py,sha256=NiX-x7LcXaPMfMfZ2W_9tX-REVfVwTzWuRl8cybgwV0,9144
socketio/base_manager.py,sha256=DJLH6IbJpaJ38jCrmIaUQVj8WUKZDYtfzjKIbQslLjw,5768
socketio/base_namespace.py,sha256=PpBylO78iyOJ6st5IHQeeXTvD7XzUim5MNDzdKjISxU,978
socketio/base_server.py,sha256=41qNT4HCB9bDjuCZibcx260AdE05wSFk6nuWmbCwxUs,7942
socketio/client.py,sha256=llrkby1Xx_i6Gp14WwBmHXavDumLkNWeP7oHKq-22uo,24475
socketio/exceptions.py,sha256=c8yKss_oJl-fkL52X_AagyJecL-9Mxlgb5xDRqSz5tA,975
socketio/kafka_manager.py,sha256=OCUBlntnqAOlqZn7YxxM0E4rt6VLd_b-wJWrVWKRR-A,2419
socketio/kombu_manager.py,sha256=qFzWOUlsIznNx2IYKMvA6GKrDcG0zle5_G9duanJ3Po,5747
socketio/manager.py,sha256=wief9dt2R_OpNKDcFr4HAppCDQfc_WTqwQQ6ViCZi4k,3826
socketio/middleware.py,sha256=P8wOgSzy3YKOcRVI-r3KNKsEejBz_f5p2wdV8ZqW12E,1591
socketio/msgpack_packet.py,sha256=0K_XXM-OF3SdqOaLN_O5B4a1xHE6N_UhhiaRhQdseNw,514
socketio/namespace.py,sha256=jkCi7n-bmAXYRdM-dFwKuLSmXqNc7YGF7SUzHGZzaKw,8870
socketio/packet.py,sha256=aVlNp2MV4QR1VfVoMN9zhIbxSTQONduDFgxVv4AzZcI,7065
socketio/pubsub_manager.py,sha256=za7RryF-FbtzOeZdPVS0Eq94C_C0LpT8SJ5iw84U6Tc,9740
socketio/redis_manager.py,sha256=KExT3uzACK42g9OuHKvzI7YpfpGGLFMOwsWhgnPkx0Q,4442
socketio/server.py,sha256=VzG3IqzYDV7hhUoQoJ-k5wGyACHxDWfvVN4xqXhZUwM,34156
socketio/simple_client.py,sha256=0s5PzsESJD1sEJtqrljnRCkh15ctrkV0WS64cNhMrrA,8325
socketio/tornado.py,sha256=2O4RUyZw7CgTBYsmcy-7SGYNngz28iqOJ3m1_zDBz1U,357
socketio/zmq_manager.py,sha256=JsRooJoH0dwOE0QsmDwePmwzsIN0VudsFWweCzScvMs,3545
