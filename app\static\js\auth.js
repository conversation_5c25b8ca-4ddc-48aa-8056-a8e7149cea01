// 认证管理

class AuthManager {
    constructor() {
        this.setupEventListeners();
        // 延迟初始化认证状态，确保所有依赖都已加载
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializeAuth());
        } else {
            this.initializeAuth();
        }
    }

    // 初始化认证状态
    initializeAuth() {
        Logger.auth('初始化认证状态');
        const token = Storage.get('auth_token');
        const sessionId = Storage.get('session_id');
        const userData = Storage.get('user_data');

        Logger.auth('从存储中恢复认证信息:', {
            hasToken: !!token,
            hasSessionId: !!sessionId,
            hasUserData: !!userData,
            user: userData?.username
        });

        if (token || sessionId) {
            // 重要：恢复API客户端的认证信息
            if (window.api) {
                window.api.setAuth(token, sessionId);
            }

            AppState.isAuthenticated = true;
            if (userData) {
                AppState.user = userData;
                AppState.isAdmin = userData.is_admin || false;
            }

            Logger.auth('认证状态已恢复:', {
                isAuthenticated: AppState.isAuthenticated,
                user: AppState.user?.username,
                isAdmin: AppState.isAdmin
            });
        } else {
            Logger.auth('未找到有效的认证信息');
        }
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 标签页切换
        DOM.$$('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });
        
        // 表单提交
        document.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                const activeForm = DOM.$('.auth-form.active');
                if (activeForm) {
                    const formId = activeForm.id;
                    if (formId === 'login-form') {
                        this.handleLogin();
                    } else if (formId === 'register-form') {
                        this.handleRegister();
                    } else if (formId === 'anonymous-form') {
                        this.handleAnonymous();
                    }
                }
            }
        });
        
        // 监听认证状态变化
        EventBus.on('auth:logout', () => {
            this.showLoginScreen();
        });
    }
    
    // 切换标签页
    switchTab(tab) {
        // 更新标签按钮状态
        DOM.$$('.tab-btn').forEach(btn => {
            DOM.removeClass(btn, 'active');
        });
        DOM.addClass(DOM.$(`[data-tab="${tab}"]`), 'active');
        
        // 更新表单显示
        DOM.$$('.auth-form').forEach(form => {
            DOM.removeClass(form, 'active');
        });
        DOM.addClass(DOM.$(`#${tab}-form`), 'active');
        
        // 清空消息
        this.clearMessage();
    }
    
    // 显示消息
    showMessage(message, type = 'info') {
        const messageEl = DOM.$('#auth-message');
        messageEl.textContent = message;
        messageEl.className = `message ${type}`;
        DOM.show(messageEl);
        
        // 3秒后自动隐藏
        setTimeout(() => {
            DOM.hide(messageEl);
        }, 3000);
    }
    
    // 清空消息
    clearMessage() {
        DOM.hide(DOM.$('#auth-message'));
    }
    
    // 处理登录
    async handleLogin() {
        const username = DOM.$('#login-username').value.trim();
        const password = DOM.$('#login-password').value;

        Logger.auth(`开始登录流程: ${username}`);

        if (!username || !password) {
            this.showMessage('请填写用户名和密码', 'error');
            return;
        }

        try {
            this.showMessage('正在登录...', 'info');

            const response = await AuthAPI.login(username, password);

            // 保存用户数据
            Storage.set('user_data', response.user);

            Logger.auth('登录成功，准备跳转到聊天界面');
            this.showMessage('登录成功！', 'success');

            // 延迟跳转到聊天界面
            setTimeout(() => {
                Logger.auth('跳转到聊天界面');
                this.showChatScreen();
            }, 1000);

        } catch (error) {
            Logger.error('AUTH', '登录失败:', error);
            this.showMessage(error.message || '登录失败，请重试', 'error');
        }
    }
    
    // 处理注册
    async handleRegister() {
        const username = DOM.$('#register-username').value.trim();
        const email = DOM.$('#register-email').value.trim();
        const password = DOM.$('#register-password').value;
        const inviteCode = DOM.$('#register-invite').value.trim();
        
        if (!username || !email || !password) {
            this.showMessage('请填写所有必填字段', 'error');
            return;
        }
        
        // 简单的邮箱验证
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            this.showMessage('请输入有效的邮箱地址', 'error');
            return;
        }
        
        // 密码强度验证
        if (password.length < 6) {
            this.showMessage('密码长度至少6位', 'error');
            return;
        }
        
        try {
            this.showMessage('正在注册...', 'info');
            
            await AuthAPI.register(username, email, password, inviteCode || null);
            
            this.showMessage('注册成功！请登录', 'success');
            
            // 切换到登录标签页
            setTimeout(() => {
                this.switchTab('login');
                DOM.$('#login-username').value = username;
            }, 1500);
            
        } catch (error) {
            console.error('注册失败:', error);
            this.showMessage(error.message || '注册失败，请重试', 'error');
        }
    }
    
    // 处理匿名登录
    async handleAnonymous() {
        const nickname = DOM.$('#anonymous-nickname').value.trim();
        
        if (!nickname) {
            this.showMessage('请输入昵称', 'error');
            return;
        }
        
        if (nickname.length > 20) {
            this.showMessage('昵称长度不能超过20个字符', 'error');
            return;
        }
        
        try {
            this.showMessage('正在创建匿名用户...', 'info');
            
            const response = await AuthAPI.createAnonymous(nickname);
            
            // 保存用户数据
            Storage.set('user_data', response.user);
            
            this.showMessage('创建成功！', 'success');
            
            // 延迟跳转到聊天界面
            setTimeout(() => {
                this.showChatScreen();
            }, 1000);
            
        } catch (error) {
            console.error('创建匿名用户失败:', error);
            this.showMessage(error.message || '创建失败，请重试', 'error');
        }
    }
    
    // 显示登录界面
    showLoginScreen() {
        DOM.hide(DOM.$('#chat-screen'));
        DOM.show(DOM.$('#login-screen'));
        DOM.addClass(DOM.$('#login-screen'), 'active');
        DOM.removeClass(DOM.$('#chat-screen'), 'active');
        
        // 清空表单
        this.clearForms();
        
        // 重置到登录标签页
        this.switchTab('login');
    }
    
    // 显示聊天界面
    showChatScreen() {
        DOM.hide(DOM.$('#login-screen'));
        DOM.show(DOM.$('#chat-screen'));
        DOM.addClass(DOM.$('#chat-screen'), 'active');
        DOM.removeClass(DOM.$('#login-screen'), 'active');
        
        // 初始化聊天界面
        this.initializeChatScreen();
        
        // 触发聊天界面初始化事件
        EventBus.emit('chat:initialized');
    }
    
    // 初始化聊天界面
    initializeChatScreen() {
        if (!AppState.user) return;
        
        // 更新用户信息显示
        const userAvatar = DOM.$('#user-avatar');
        const userName = DOM.$('#user-name');
        const userStatus = DOM.$('#user-status');
        
        if (userAvatar) {
            userAvatar.textContent = AppState.user.nickname ? 
                AppState.user.nickname.charAt(0).toUpperCase() : '👤';
        }
        
        if (userName) {
            userName.textContent = AppState.user.nickname || AppState.user.username || '用户';
        }
        
        if (userStatus) {
            userStatus.textContent = AppState.user.is_anonymous ? '匿名用户' : '注册用户';
        }
        
        // 显示/隐藏管理员功能
        const adminElements = DOM.$$('.admin-only');
        adminElements.forEach(el => {
            if (AppState.isAdmin) {
                DOM.show(el);
            } else {
                DOM.hide(el);
            }
        });
    }
    
    // 清空表单
    clearForms() {
        const inputs = DOM.$$('#login-screen input');
        inputs.forEach(input => {
            input.value = '';
        });
    }
    
    // 退出登录
    async logout() {
        try {
            await AuthAPI.logout();
            
            // 清空本地数据
            Storage.remove('user_data');
            
            // 重置应用状态
            AppState.user = null;
            AppState.currentRoom = null;
            AppState.rooms = [];
            AppState.messages.clear();
            
            // 断开Socket连接
            if (AppState.socket) {
                AppState.socket.disconnect();
                AppState.socket = null;
            }
            
            // 显示登录界面
            this.showLoginScreen();
            
            showNotification('已退出登录', 'info');
            
        } catch (error) {
            console.error('退出登录失败:', error);
            // 即使退出请求失败，也要清空本地状态
            this.showLoginScreen();
        }
    }
    
    // 检查认证状态
    isAuthenticated() {
        return AppState.isAuthenticated && (Storage.get('auth_token') || Storage.get('session_id'));
    }
    
    // 获取当前用户
    getCurrentUser() {
        return AppState.user;
    }
}

// 全局函数
window.handleLogin = function() {
    window.authManager.handleLogin();
};

window.handleRegister = function() {
    window.authManager.handleRegister();
};

window.handleAnonymous = function() {
    window.authManager.handleAnonymous();
};

window.logout = function() {
    window.authManager.logout();
};

// 初始化认证管理器
document.addEventListener('DOMContentLoaded', () => {
    window.authManager = new AuthManager();
});
