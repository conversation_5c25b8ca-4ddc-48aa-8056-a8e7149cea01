<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证修复测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .log-output { background: #f5f5f5; padding: 10px; margin: 10px 0; height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        button { margin: 5px; padding: 8px 15px; }
        input { margin: 5px; padding: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .debug { color: gray; }
    </style>
</head>
<body>
    <h1>认证修复测试页面</h1>
    
    <div class="test-section">
        <h3>1. 日志系统测试</h3>
        <button onclick="testLogger()">测试日志系统</button>
        <div id="log-output" class="log-output"></div>
    </div>
    
    <div class="test-section">
        <h3>2. 存储系统测试</h3>
        <button onclick="testStorage()">测试存储系统</button>
        <button onclick="clearStorage()">清空存储</button>
        <div id="storage-output" class="log-output"></div>
    </div>
    
    <div class="test-section">
        <h3>3. 认证测试</h3>
        <input type="text" id="username" placeholder="用户名" value="admin">
        <input type="password" id="password" placeholder="密码" value="admin123">
        <button onclick="testLogin()">测试登录</button>
        <button onclick="testApiCall()">测试API调用</button>
        <button onclick="checkAuthState()">检查认证状态</button>
        <div id="auth-output" class="log-output"></div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="http://localhost:5000/static/js/socket.io.min.js"></script>
    <script src="http://localhost:5000/static/js/utils.js"></script>
    <script src="http://localhost:5000/static/js/api.js"></script>
    
    <script>
        // 重定向console.log到页面显示
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToOutput(outputId, message, type = 'info') {
            const output = document.getElementById(outputId);
            const div = document.createElement('div');
            div.className = type;
            div.textContent = new Date().toLocaleTimeString() + ' - ' + message;
            output.appendChild(div);
            output.scrollTop = output.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToOutput('log-output', args.join(' '), 'info');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToOutput('log-output', args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToOutput('log-output', args.join(' '), 'debug');
        };
        
        // 测试函数
        function testLogger() {
            document.getElementById('log-output').innerHTML = '';
            Logger.debug('TEST', '这是一个调试消息');
            Logger.info('TEST', '这是一个信息消息');
            Logger.warn('TEST', '这是一个警告消息');
            Logger.error('TEST', '这是一个错误消息');
            Logger.auth('这是一个认证消息');
            Logger.api('这是一个API消息');
            Logger.socket('这是一个Socket消息');
        }
        
        function testStorage() {
            document.getElementById('storage-output').innerHTML = '';
            const output = document.getElementById('storage-output');
            
            // 测试存储
            Storage.set('test_key', { message: 'Hello World', timestamp: Date.now() });
            const value = Storage.get('test_key');
            addToOutput('storage-output', '存储测试: ' + JSON.stringify(value), 'info');
            
            // 测试认证相关存储
            const token = Storage.get('auth_token');
            const userData = Storage.get('user_data');
            addToOutput('storage-output', '认证Token: ' + (token ? '存在' : '不存在'), token ? 'success' : 'error');
            addToOutput('storage-output', '用户数据: ' + JSON.stringify(userData), userData ? 'success' : 'error');
        }
        
        function clearStorage() {
            Storage.clear();
            addToOutput('storage-output', '存储已清空', 'info');
        }
        
        async function testLogin() {
            document.getElementById('auth-output').innerHTML = '';
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                addToOutput('auth-output', '开始登录测试...', 'info');
                const response = await AuthAPI.login(username, password);
                addToOutput('auth-output', '登录成功: ' + JSON.stringify(response), 'success');
            } catch (error) {
                addToOutput('auth-output', '登录失败: ' + error.message, 'error');
            }
        }
        
        async function testApiCall() {
            try {
                addToOutput('auth-output', '测试API调用...', 'info');
                const response = await RoomsAPI.getRooms();
                addToOutput('auth-output', 'API调用成功: ' + JSON.stringify(response), 'success');
            } catch (error) {
                addToOutput('auth-output', 'API调用失败: ' + error.message, 'error');
            }
        }
        
        function checkAuthState() {
            const state = {
                isAuthenticated: AppState.isAuthenticated,
                user: AppState.user,
                hasToken: !!Storage.get('auth_token'),
                hasUserData: !!Storage.get('user_data')
            };
            addToOutput('auth-output', '认证状态: ' + JSON.stringify(state, null, 2), 'info');
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            addToOutput('log-output', '页面加载完成，日志系统已初始化', 'success');
            checkAuthState();
        });
    </script>
</body>
</html>
