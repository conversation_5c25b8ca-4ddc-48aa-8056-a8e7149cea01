<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天平台</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/components.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/responsive.css') }}">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💬</text></svg>">
</head>
<body>
    <!-- 加载动画 -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-spinner"></div>
        <div class="loading-text">聊天平台加载中...</div>
    </div>

    <!-- 主应用容器 -->
    <div id="app" class="app-container" style="display: none;">
        <!-- 登录界面 -->
        <div id="login-screen" class="screen">
            <div class="login-container">
                <div class="login-header">
                    <h1>💬 聊天平台</h1>
                    <p>连接你我，分享精彩</p>
                </div>

                <div class="login-tabs">
                    <button class="tab-btn active" data-tab="login">登录</button>
                    <button class="tab-btn" data-tab="register">注册</button>
                    <button class="tab-btn" data-tab="anonymous">匿名进入</button>
                </div>

                <!-- 登录表单 -->
                <div id="login-form" class="auth-form active">
                    <div class="form-group">
                        <input type="text" id="login-username" placeholder="用户名" required>
                    </div>
                    <div class="form-group">
                        <input type="password" id="login-password" placeholder="密码" required>
                    </div>
                    <button class="btn btn-primary" onclick="handleLogin()">登录</button>
                </div>

                <!-- 注册表单 -->
                <div id="register-form" class="auth-form">
                    <div class="form-group">
                        <input type="text" id="register-username" placeholder="用户名" required>
                    </div>
                    <div class="form-group">
                        <input type="email" id="register-email" placeholder="邮箱" required>
                    </div>
                    <div class="form-group">
                        <input type="password" id="register-password" placeholder="密码" required>
                    </div>
                    <div class="form-group">
                        <input type="text" id="register-invite" placeholder="邀请码（可选）">
                    </div>
                    <button class="btn btn-primary" onclick="handleRegister()">注册</button>
                </div>

                <!-- 匿名进入表单 -->
                <div id="anonymous-form" class="auth-form">
                    <div class="form-group">
                        <input type="text" id="anonymous-nickname" placeholder="昵称" required>
                    </div>
                    <button class="btn btn-secondary" onclick="handleAnonymous()">匿名进入</button>
                </div>

                <div id="auth-message" class="message"></div>
            </div>
        </div>

        <!-- 主聊天界面 -->
        <div id="chat-screen" class="screen">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="sidebar-header">
                    <div class="user-info">
                        <div class="user-avatar" id="user-avatar">👤</div>
                        <div class="user-details">
                            <div class="user-name" id="user-name">用户名</div>
                            <div class="user-status" id="user-status">在线</div>
                        </div>
                    </div>
                    <div class="sidebar-actions">
                        <button class="btn-icon" onclick="showSettings()" title="设置">⚙️</button>
                        <button class="btn-icon" onclick="logout()" title="退出">🚪</button>
                    </div>
                </div>

                <div class="sidebar-nav">
                    <button class="nav-btn active" data-view="rooms">🏠 房间</button>
                    <button class="nav-btn" data-view="profile">👤 个人信息</button>
                    <button class="nav-btn" data-view="questionnaires">📋 问卷</button>
                    <button class="nav-btn admin-only" data-view="admin" style="display: none;">⚙️ 管理</button>
                </div>

                <!-- 房间列表 -->
                <div id="rooms-view" class="sidebar-content active">
                    <div class="content-header">
                        <h3>房间列表</h3>
                        <button class="btn-icon" onclick="showCreateRoom()" title="创建房间">➕</button>
                    </div>
                    <div id="rooms-list" class="rooms-list">
                        <!-- 房间列表将在这里动态加载 -->
                    </div>
                </div>

                <!-- 个人信息视图 -->
                <div id="profile-view" class="sidebar-content">
                    <div class="content-header">
                        <h3>个人信息</h3>
                        <button class="btn-icon" onclick="editProfile()" title="编辑">✏️</button>
                    </div>
                    <div id="profile-content">
                        <!-- 个人信息内容 -->
                    </div>
                </div>

                <!-- 问卷视图 -->
                <div id="questionnaires-view" class="sidebar-content">
                    <div class="content-header">
                        <h3>问卷系统</h3>
                        <button class="btn-icon" onclick="createQuestionnaire()" title="创建问卷">➕</button>
                    </div>
                    <div id="questionnaire-content">
                        <!-- 问卷内容 -->
                    </div>
                </div>

                <!-- 管理员视图 -->
                <div id="admin-view" class="sidebar-content">
                    <div class="content-header">
                        <h3>系统管理</h3>
                    </div>
                    <div id="admin-content">
                        <!-- 管理员内容 -->
                    </div>
                </div>
            </div>

            <!-- 主聊天区域 -->
            <div class="main-content">
                <!-- 聊天头部 -->
                <div class="chat-header">
                    <div class="room-info">
                        <h2 id="current-room-name">选择一个房间开始聊天</h2>
                        <div class="room-members" id="room-members"></div>
                    </div>
                    <div class="chat-actions">
                        <button class="btn-icon" onclick="showRoomSettings()" title="房间设置">⚙️</button>
                        <button class="btn-icon mobile-menu-toggle" onclick="toggleSidebar()" title="菜单">☰</button>
                    </div>
                </div>

                <!-- 消息区域 -->
                <div class="messages-container" id="messages-container">
                    <div class="welcome-message">
                        <h3>欢迎来到聊天平台！</h3>
                        <p>选择一个房间开始聊天，或创建一个新房间。</p>
                    </div>
                </div>

                <!-- 输入区域 -->
                <div class="input-area" id="input-area" style="display: none;">
                    <div class="input-actions">
                        <button class="btn-icon" onclick="showFileUpload()" title="上传文件">📎</button>
                        <button class="btn-icon" onclick="showEmoji()" title="表情">😊</button>
                    </div>
                    <div class="input-wrapper">
                        <textarea id="message-input" placeholder="输入消息..." rows="1"></textarea>
                        <button class="send-btn" onclick="sendMessage()" title="发送">📤</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框容器 -->
    <div id="modal-container"></div>

    <!-- 通知容器 -->
    <div id="notifications-container"></div>

    <!-- JavaScript -->
    <script src="/socket.io/socket.io.js"></script>
    <script src="{{ url_for('static', filename='js/utils.js') }}"></script>
    <script src="{{ url_for('static', filename='js/api.js') }}"></script>
    <script src="{{ url_for('static', filename='js/auth.js') }}"></script>
    <script src="{{ url_for('static', filename='js/chat.js') }}"></script>
    <script src="{{ url_for('static', filename='js/rooms.js') }}"></script>
    <script src="{{ url_for('static', filename='js/profile.js') }}"></script>
    <script src="{{ url_for('static', filename='js/questionnaire.js') }}"></script>
    <script src="{{ url_for('static', filename='js/file.js') }}"></script>
    <script src="{{ url_for('static', filename='js/admin.js') }}"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
